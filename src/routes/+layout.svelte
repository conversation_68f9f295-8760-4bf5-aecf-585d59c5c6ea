<script lang="ts">
  import Footer from "$lib/components/Footer.svelte";
  import Toaster from "$lib/components/Toaster.svelte";
  import "../styles.css";
  import { APP_VERSION } from "../version.js";
  import { publicConfig } from "../publicConfig.ts";

  const { children } = $props();

  const CURRENT_TEST_VERSION = APP_VERSION;
</script>

<div class="page-container">
  <a href="#main-content" class="skip-link">Skip to Main Content</a>

  <main id="main-content">
    {#if ["dev", "staging"].includes(publicConfig.env ?? "")}
      <div class="version-identifier">
        Current test version: {CURRENT_TEST_VERSION}
      </div>
    {/if}
    {@render children()}
  </main>

  <Footer />
</div>

<Toaster />

<style>
  .page-container {
    display: flex;
    flex-direction: column;
    min-height: 100dvh;
  }

  .version-identifier {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 100;
    border-radius: var(--radius-sm);
    background-color: rgba(255, 255, 255, 0.75);
    padding: var(--padding-xs);
    color: black;
  }

  main {
    flex: 1;
    background-color: var(--main-bg-color);
  }
</style>
