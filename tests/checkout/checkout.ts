import { expect, type Page } from "@playwright/test";
import { checkItemInCart, getCart, openItemDialog } from "../items";

export const addItemToCart = async (page: Page, isMobile: boolean) => {
  const itemName = "Caesar Salad";
  const dialog = await openItemDialog(page, itemName);
  await expect(
    dialog.getByRole("checkbox", { name: "Bacon Bits" }),
  ).toBeVisible();
  await dialog.getByRole("button", { name: "Add to Cart" }).click();
  await checkItemInCart({ page, itemName, modifiers: [], isMobile });

  await getCart(page, isMobile);
};

export const retryFetchingEmails = async (
  expectedCount = 1,
  timeout = 5000,
) => {
  const MAX_RETRIES = 5;
  let iteration = 0;

  while (iteration < MAX_RETRIES) {
    let response = null;
    try {
      response = await fetch("http://localhost:1080/api/messages");
    } catch (err) {
      console.error(err);
    }

    if (!response || !response.ok) {
      iteration++;
      await new Promise((res) => setTimeout(res, timeout));
      continue;
    }

    const messages = await response.json();
    if (messages.length >= expectedCount) {
      return messages;
    }
    // wait before retrying
  }
  throw new Error("No email messages received within timeout");
};

/**
 * Fills CollectJS card info
 * @param page
 * @param tabAfter CollectJS doesn't validate the fields unless you press tab after the last element
 */
export const fillCardInfo = async (page: Page, tabAfter = true) => {
  await page.waitForSelector("iframe");

  //  https://stackoverflow.com/questions/68837736/fill-stripe-elements-card-with-playwright
  const cardFrame = page.frameLocator('iframe[src*="elementId=ccnumber"]');
  await cardFrame
    .locator('input[placeholder="0000 0000 0000 0000"]')
    .fill("4111 1111 1111 1111");

  const expFrame = page.frameLocator('iframe[src*="elementId=ccexp"]');
  await expFrame.locator('input[placeholder="MM / YY"]').fill("12/25");

  const cvvFrame = page.frameLocator('iframe[src*="elementId=cvv"]');
  await cvvFrame.locator('input[placeholder="***"]').fill("999");

  if (tabAfter) {
    await page.keyboard.press("Tab");
  }
};

export const fillPersonalInfo = async (page: Page, email?: string) => {
  await page.getByLabel("First Name").fill("Bob");
  await page.getByLabel("Last Name").fill("Smith");
  await page.getByLabel("Email").fill(email ?? "<EMAIL>");
  await page.getByLabel("Phone").fill("1234567890");
};

export const fullyCompleteForm = async (page: Page, email?: string) => {
  await fillCardInfo(page);

  const continueButton = page.getByRole("button", { name: "Continue" });
  await expect(continueButton).toBeVisible();
  await continueButton.click();

  await fillPersonalInfo(page, email);
};
